import React, { useState } from 'react';
import { Button, Input, Card, capitalize, chunk } from '@monorepo/ui-components';

const App: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [items, setItems] = useState<string[]>([
    'apple', 'banana', 'cherry', 'date', 'elderberry', 'fig'
  ]);

  const handleAddItem = () => {
    if (inputValue.trim()) {
      setItems([...items, inputValue.trim()]);
      setInputValue('');
    }
  };

  const handleClearItems = () => {
    setItems([]);
  };

  const chunkedItems = chunk(items, 3);

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>
        {capitalize('monorepo example app')}
      </h1>
      
      <div style={{ marginBottom: '20px' }}>
        <Card
          title="Add Items"
          subtitle="Use the form below to add new items to the list"
        >
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-end' }}>
          <div style={{ flex: 1 }}>
            <Input
              label="New Item"
              placeholder="Enter an item name"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleAddItem();
                }
              }}
            />
          </div>
          <Button onClick={handleAddItem} disabled={!inputValue.trim()}>
            Add Item
          </Button>
        </div>
        </Card>
      </div>

      <Card title="Items List" subtitle={`Total items: ${items.length}`}>
        <div style={{ marginBottom: '15px' }}>
          <Button 
            variant="danger" 
            size="small" 
            onClick={handleClearItems}
            disabled={items.length === 0}
          >
            Clear All
          </Button>
        </div>
        
        {items.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No items yet. Add some above!</p>
        ) : (
          <div>
            {chunkedItems.map((chunk, chunkIndex) => (
              <div 
                key={chunkIndex} 
                style={{ 
                  display: 'flex', 
                  gap: '10px', 
                  marginBottom: '10px',
                  flexWrap: 'wrap'
                }}
              >
                {chunk.map((item, itemIndex) => (
                  <div
                    key={`${chunkIndex}-${itemIndex}`}
                    style={{
                      flex: '1',
                      minWidth: '150px'
                    }}
                  >
                    <Card
                      hoverable
                      onClick={() => {
                        setItems(items.filter(i => i !== item));
                      }}
                    >
                    <div style={{ textAlign: 'center' }}>
                      <strong>{capitalize(item)}</strong>
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        Click to remove
                      </div>
                    </div>
                    </Card>
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}
      </Card>

      <div style={{ marginTop: '20px' }}>
        <Card
          title="About This App"
          subtitle="Demonstrating monorepo packages"
        >
        <p>This example app demonstrates:</p>
        <ul>
          <li><strong>@monorepo/utils</strong>: Using <code>capitalize</code> and <code>chunk</code> functions</li>
          <li><strong>@monorepo/ui-components</strong>: Using <code>Button</code>, <code>Input</code>, and <code>Card</code> components</li>
          <li><strong>TypeScript</strong>: Full type safety across packages</li>
          <li><strong>Rollup</strong>: Bundling with shared configuration</li>
        </ul>
        </Card>
      </div>
    </div>
  );
};

export default App;
